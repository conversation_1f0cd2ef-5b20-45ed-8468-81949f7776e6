import {DataController} from '../../base/baseController';
import {categoryAction} from '../../redux/actions/categoryAction';
import {Category} from '../../redux/models/category';

class categoryDA {
  private categoryController: DataController;
  constructor() {
    this.categoryController = new DataController('Category');
  }
  async getAll() {
    const response = await this.categoryController.getListSimple({
      query: '*',
      returns: ['Id', 'Name', 'Img'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }
  async getCategoriesParent() {
    const response = await this.categoryController.getListSimple({
      query: '-@ParentId',
      returns: ['Id', 'Name', 'Img'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getCategories() {
    const response = await this.categoryController.getListSimple({
      query: '*',
      returns: ['Id', 'Name', 'Img', 'ParentId'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getChildCategories(parentId: string) {
    const response = await this.categoryController.getListSimple({
      query: `@ParentId: {${parentId}}`,
      returns: ['Id', 'Name', 'Img', 'ParentId'],
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getCategoriesByParentId() {
    const response = await categoryAction.find({
      page: 1,
      size: 1000,
    });
    if (response?.length > 0) {
      return response.filter((i: Category) => !i.ParentId);
    }
    return null;
  }
}

export default new categoryDA();
