import React, {useRef, useCallback, useEffect, useState} from 'react';
import {
  Text,
  useWindowDimensions,
  RefreshControl,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {ComponentStatus, FDialog, showSnackbar} from 'wini-mobile-components';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch} from 'react-redux';

// Internal imports
import {ColorThemes} from '../../assets/skin/colors';
import {RootScreen} from '../../router/router';
import {CartActions} from '../../redux/reducers/CartReducer';
import {AppDispatch} from '../../redux/store/store';
import {handleShopInfoPress} from './Component/Ultis';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {updateFavoriteProduct} from '../../redux/actions/productAction';
import SuggestionProductSection from './section/SuggestionProductSection';

// Import from ProductDetailModule
import {
  ProductDetailSkeleton,
  ProductDetailHeader,
  ProductImageCarousel,
  ProductPriceSection,
  ProductInfoSection,
  ShippingSection,
  RatingSection,
  SellerInfoSection,
  ShopInfoSection,
  RelatedProductsSection,
  ProductDescriptionSection,
  BottomActionBar,
  useProductDetail,
} from './ProductDetailModule';
import DropdownViolation from './Component/DropdownViolation';
import ReportModal from './Modal/ReportModal';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {ReportType} from '../../Config/Contanst';
import ChatAPI from '../chat/services/ChatAPI';
import {addChatRoom} from '../../redux/reducers/ChatReducer';
import {navigate} from '../../router/router';
import store from '../../redux/store/store';

export default function ProductDetail() {
  // Hooks
  const route = useRoute<any>();
  const {id} = route.params;
  const navigation = useNavigation<any>();
  const {width: windowWidth} = useWindowDimensions();
  const dispatch: AppDispatch = useDispatch();

  // Refs
  const scrollviewRef = useRef<ScrollView>(null);
  const DialogRef = useRef<any>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  // Redux state
  const customerAddress = useSelectorCustomerState().myAddress;
  const customer = useSelectorCustomerState().data;
  const reportController = new DataController('Report');

  // Use custom hook for product detail logic
  const {
    loading,
    refreshing,
    data,
    shop,
    productImages,
    like,
    setLike,
    onRefresh,
    setLoading,
  } = useProductDetail(id);

  // Callbacks
  const handleProductPress = useCallback(
    (productId: string) => {
      navigation.push(RootScreen.ProductDetail, {id: productId});
    },
    [navigation],
  );

  useEffect(() => {
    setLoading(true);
    scrollToTop();
  }, [id]);

  const scrollToTop = useCallback(() => {
    scrollviewRef.current?.scrollTo({y: 0, animated: true});
  }, []);

  const handleFavoritePress = useCallback(() => {
    if (data) {
      dispatch(updateFavoriteProduct({...data, IsFavorite: !like} as any));
      setLike(!like);
    }
  }, [data, like, dispatch, setLike]);

  const handleSharePress = useCallback(() => {
    // TODO: Implement share functionality
  }, []);

  const handleChatPress = async () => {
    try {
      if (!customer?.Id && !customer?.id) {
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể xác định người dùng hiện tại',
        });
        return;
      }

      if (!shop?.Id) {
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không tìm thấy thông tin cửa hàng',
        });
        return;
      }

      // Tạo contact object từ thông tin shop
      const contact = {
        Id: shop.Id,
        Name: shop.Name || 'Cửa hàng',
        AvatarUrl: shop.Img || null,
      };

      const currentUserId = customer.Id || customer.id!;

      // Tìm hoặc tạo ChatRoom
      const chatRoom = await ChatAPI.findOrCreatePrivateRoom(currentUserId, contact);
      const listRoom = store.getState().chat.rooms;
      if (!listRoom.find((item: any) => item.id === chatRoom.id)) {
        dispatch(addChatRoom(chatRoom));
      }

      // Navigate đến ChatRoomScreen
      navigate(RootScreen.ChatRoom, { room: chatRoom });

    } catch (error) {
      console.error('Error creating chat:', error);
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tạo cuộc trò chuyện',
      });
    }
  }

  const handleAddToCart = useCallback(() => {
    if (data) {
      dispatch(CartActions.addItemToCart(data, 1));
    }
  }, [data, dispatch]);

  const handleBuyNow = useCallback(() => {
    if (!customer) {
      dialogCheckAcc(DialogRef);
      return;
    }
    if (data && shop) {
      navigation.navigate(RootScreen.CheckoutPage, {
        items: [
          {
            ...data,
            ProductId: data?.Id,
            Quantity: 1,
            ShopId: shop?.Id,
            ShopName: shop?.Name,
            ShopAvatar: shop?.Img,
          },
        ],
        address: customerAddress?.find((item: any) => item.IsDefault),
      });
    }
  }, [customer, data, shop, navigation, customerAddress, DialogRef]);

  const handleSellerPress = useCallback(() => {
    if (shop) {
      navigation.navigate(RootScreen.InforShopView, {shop: shop});
    }
  }, [navigation, shop]);

  const handleViewAllRatings = useCallback(() => {
    navigation.navigate(RootScreen.RatingScreen, {
      id: data?.Id,
      rate: data?.rating,
      countRate: data?.countRate,
    });
  }, [navigation, data]);

  const handleShopInfoPressLocal = useCallback(
    (type: 'rating' | 'product' | 'order') => {
      handleShopInfoPress(type, shop);
    },
    [shop, navigation],
  );

  // Show skeleton while loading
  if (loading) {
    return <ProductDetailSkeleton />;
  }

  const handleReportPress = async (text: string) => {
    const dataReport = {
      Id: randomGID(),
      DateCreated: new Date().getTime(),
      CustomerId: customer?.Id,
      ProductId: data?.Id,
      Name: data?.Name,
      Type: ReportType.product,
      Content: text ?? 'Không có lý do báo cáo',
    };
    const result = await reportController.add([dataReport]);
    if (result.code === 200) {
      showSnackbar({
        message: 'Đã báo cáo vi phạm',
        status: ComponentStatus.SUCCSESS,
      });
    }
  };

  const handleGoback = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <FDialog ref={DialogRef} />

      {/* Header */}
      <ProductDetailHeader
        onBack={() => navigation.goBack()}
        onCartPress={() => navigation.navigate(RootScreen.CartPage)}
        onMenuPress={() => setShowDropdown(!showDropdown)}
      />

      {/* Dropdown Menu */}
      {showDropdown && (
        <DropdownViolation
          setShowDropdown={setShowDropdown}
          setOpenModal={setOpenModal}
          handleGoback={handleGoback}
        />
      )}
      <ScrollView
        ref={scrollviewRef}
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        style={styles.scrollView}
        removeClippedSubviews={true}
        scrollEventThrottle={16}>
        {/* Product Image Carousel */}
        <ProductImageCarousel images={productImages} />

        {/* Price and Actions */}
        <ProductPriceSection
          price={data?.Price}
          discount={data?.Discount}
          isFavorite={like}
          onFavoritePress={handleFavoritePress}
          onSharePress={handleSharePress}
        />

        {/* Product Title */}
        <Text style={styles.productTitle}>{data?.Name || ''}</Text>

        {/* Product Details */}
        <ProductInfoSection
          brandName={data?.BrandName}
          sold={data?.Sold || 0}
          inStock={data?.InStock || 0}
          Unit={data?.Unit || ''}
        />

        {/* Shipping */}
        <ShippingSection isFreeShip={data?.IsFreeShip} />

        {/* Rating */}
        <RatingSection
          rating={data?.rating}
          onViewAllPress={handleViewAllRatings}
        />

        {/* Seller Info */}
        <SellerInfoSection shop={shop} onPress={handleSellerPress} />

        {/* Shop Info */}
        <ShopInfoSection
          rating={shop?.rating}
          totalProducts={shop?.totalProducts}
          totalOrder={shop?.totalOrder}
          onPress={handleShopInfoPressLocal}
        />

        {/* Related Products */}
        <RelatedProductsSection
          products={shop?.products}
          onProductPress={handleProductPress}
        />

        {/* Product Description */}
        <ProductDescriptionSection
          content={data?.Content}
          windowWidth={windowWidth}
        />

        {/* Suggestion Products */}
        <View style={{paddingHorizontal: 16}}>
          <SuggestionProductSection
            categoryId={data?.CategoryId}
            onSeeAllPress={() => {
              navigation.navigate(RootScreen.ProductListByCategory, {
                categoryId: data?.CategoryId,
                categoryName: data?.CategoryName,
              });
            }}
            onRefresh={refreshing}
            scrollEnabled={false}
          />
        </View>
        <View style={{height: 20}} />
      </ScrollView>

      {/* Report Modal */}
      <ReportModal
        visible={openModal}
        onClose={() => setOpenModal(false)}
        onConfirm={handleReportPress}
      />

      {/* Bottom Actions */}
      <SafeAreaView>
        <BottomActionBar
          onChatPress={handleChatPress}
          onAddToCartPress={handleAddToCart}
          onBuyNowPress={handleBuyNow}
        />
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollView: {
    flex: 1,
  },
  productTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    lineHeight: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
  },
});
