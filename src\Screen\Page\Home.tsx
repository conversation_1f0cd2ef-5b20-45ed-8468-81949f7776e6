/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  Animated,
  NativeScrollEvent,
  RefreshControl,
  StyleSheet,
  View,
} from 'react-native';
import {FBottomSheet} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import {HomeHeader} from '../Layout/headers/HomeHeader';
import {ScrollView} from 'react-native-gesture-handler';
import CategoryGrid from '../../modules/category/CategoryGrid';
import ProductBestSeller from '../../modules/Product/productBestSeller';
import HotProductsSection from '../../modules/Product/HotProductsSection';
import PointHome from '../../modules/wallet/pointHome';
import Missions from '../../modules/customer/listview/missions';
import NewsEventSection from '../../modules/news/section/NewsEventSection';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import DefaultBanner from '../../modules/banner/Banner';
import LatestNewsSection from '../../modules/news/section/LatestNewsSection';
import MuchSearchSearch from '../../modules/Product/section/MuchSearchSearch';

const Home = () => {
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current; // Initial opacity value
  const customer = useSelectorCustomerState().data;
  // Function to start the fade-in animation
  const fadeIn = () => {
    // Reset opacity to 0
    fadeAnim.setValue(0);

    // Start the fade-in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000, // 1 second fade-in
      useNativeDriver: true,
    }).start();
  };

  // State to trigger refreshes in child components
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshing(true);

    setTimeout(() => {
      setRefreshing(false);
    }, 2000);

    // Trigger refresh in child components by updating the refreshTrigger
    // This will cause the useEffect to run and call getBanner()
    setRefreshTrigger(prev => prev + 1);
  };

  const handleLoadMore = () => {
    if (!isLoadMore && hasMore) {
      setLoadMore(true);
    }
  };

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: NativeScrollEvent) => {
    const paddingToBottom = 50;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };

  // Start fade animation when component mounts
  useEffect(() => {
    fadeIn();
  }, []);

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <HomeHeader
        onSearchPress={() => {
          navigation.navigate(RootScreen.SearchIndex, {type: 'product&news'});
        }}
      />
      {/* Main content */}
      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        onScroll={({nativeEvent}) => {
          if (isCloseToBottom(nativeEvent)) {
            handleLoadMore();
          }
        }}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }>
        {/* Điểm thưởng */}
        {customer?.Id && <PointHome key={`PointHome-${refreshTrigger}`} />}

        {/* Nhiệm vụ */}
        {customer?.Id && <Missions key={`Missions-${refreshTrigger}`} />}

        <View style={{paddingHorizontal: 16}}>
          {/* Danh mục */}
          <View style={styles.categorySection}>
            <CategoryGrid
              // categories={homeCategories}
              numColumns={3}
              onCategoryPress={category => {
                // Xử lý khi nhấn vào danh mục
                navigate(RootScreen.ProductListByCategory, {
                  categoryId: category.Id,
                  categoryName: category.Name,
                });
              }}
            />
          </View>
          {/* Sản phẩm HOT */}
          <MuchSearchSearch
            key={`MuchSearchSearch-${refreshTrigger}`} // Force re-render on refresh
            title="Sản phẩm HOT"
            onSeeMore={onSeeMore}
            onRefresh={refreshing}
            isHot={true}
          />

          {/* banner */}
          <DefaultBanner
            type={1}
            position={1}
            key={`DefaultBanner-${refreshTrigger}`}
          />

          {/* Sản phẩm bán chạy */}
          <ProductBestSeller
            horizontal={true}
            isSeeMore={true}
            onRefresh={refreshing}
            onPressSeeMore={() => {
              navigation.navigate(RootScreen.ProductListByCategory);
            }}
            key={`ProductBestSeller-${refreshTrigger}`} // Force re-render on refresh
          />

          {/* sự kiện */}
          <NewsEventSection isRefresh={refreshing} />
        </View>
        <View style={styles.sectionContainer}>
          <LatestNewsSection
            isRefresh={refreshing}
            isLoadMore={isLoadMore}
            onLoadMoreEnd={hasMoreData => {
              setLoadMore(false);
              setHasMore(hasMoreData);
            }}
            key={`LatestNewsSection-${refreshTrigger}`} // Force re-render on refresh
          />
          <View style={{height: 100}} />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
  },
  scrollContent: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
    height: '100%',
    marginTop: 10,
  },
  categorySection: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginTop: 15,
  },
  titleText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  textContainer: {
    paddingRight: 10,
  },
  mainText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  sectionContainer: {
    marginVertical: 12,
  },
});

export default Home;
