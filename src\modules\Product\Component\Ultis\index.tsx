import { navigate, RootScreen } from "../../../../router/router";
import { ShopData } from "../../ProductDetailModule";

export const handleShopInfoPress = (type: 'rating' | 'product' | 'order', shop: ShopData | null) => {
  if (type === 'rating')
    navigate(RootScreen.RatingScreen, {
      shopId: shop?.Id,
      rate: shop?.rating,
      countRate: shop?.countRate,
    });
  if (type === 'product')
    navigate(RootScreen.ProductShopScreen, {id: shop?.Id});
  if (type === 'order')
    navigate(RootScreen.OrderDetailPageForShop, {id: shop?.Id});
};