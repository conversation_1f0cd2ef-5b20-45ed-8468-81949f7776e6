import React, {useEffect} from 'react';
import {StatusBar} from 'react-native';

import {Provider} from 'react-redux';
import store from './src/redux/store/store';
import {NavigationContainer} from '@react-navigation/native';
import {navigationRef} from './src/router/router';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {LanguageProvider} from './src/locales/languageContext';
import notifee from '@notifee/react-native';
import {
  initNotificationPermission,
  registerListenerWithFCM,
} from './src/features/notifications/fcm/fcm_helper';
import {PaperProvider} from 'react-native-paper';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {FSnackbar} from 'wini-mobile-components';
import {DrawerMain} from './src/Screen/Layout/navigation/drawerNavigation';
import {initBiometrics} from './src/features/local-authen/local-authen';
import DrawerCategories from './src/modules/category/DrawerCategories';
import {CartActions} from './src/redux/reducers/CartReducer';
import IncomingCallProvider from './src/components/call/IncomingCallProvider';
import MessageNotificationProvider from './src/components/chat/MessageNotificationProvider';
import NotificationBadgeListener from './src/components/NotificationBadgeListener';

function App(): React.JSX.Element {
  useEffect(() => {
    notifee.setBadgeCount(0).then(() => console.log('Badge count removed'));
  }, []);

  /** setup firebase cloud message */
  useEffect(() => {
    initNotificationPermission();
    initBiometrics();
  }, []);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);

  // Khởi tạo giỏ hàng với dữ liệu mẫu và tải giỏ hàng từ AsyncStorage khi ứng dụng khởi động
  useEffect(() => {
    const initCart = async () => {
      // Khởi tạo giỏ hàng với dữ liệu mẫu nếu chưa có
      // await initializeCart();
      // await clearCartStorage();
      // Tải giỏ hàng từ AsyncStorage
      store.dispatch(CartActions.loadCartFromStorage());
    };

    initCart();
  }, []);

  const [isLoading, setLoading] = React.useState(true);

  useEffect(() => {
    // const colorData = new TableController('designtoken');
    // colorData.getAll().then(res => {
    //   if (res.code == 200) {
    //     const designTokens = res.data.map((e: any) => {
    //       return {
    //         ...e,
    //         Value: typeof e.Value === 'string' ? JSON.parse(e.Value) : e.Value,
    //       };
    //     });
    //     const tokenValues = designTokens.filter((e: any) => e.Type !== 'group');
    //     tokenValues.forEach((element: any) => {
    //       ColorThemes.light[
    //         element.Name.replace('--', '').replaceAll('-', '_')
    //       ] = element.Value?.lightMode;
    //       ColorThemes.dark[
    //         element.Name.replace('--', '').replaceAll('-', '_')
    //       ] = element.Value?.darkMode;
    //     });
    //   }

    // });
    setLoading(false);
  }, []);

  return (
    <Provider store={store} stabilityCheck="always">
      <LanguageProvider>
        <PaperProvider>
          <GestureHandlerRootView>
            <StatusBar barStyle={'dark-content'} backgroundColor={'white'} />
            <SafeAreaProvider>
              <IncomingCallProvider>
                <NotificationBadgeListener>
                  <MessageNotificationProvider>
                    <NavigationContainer ref={navigationRef}>
                      <DrawerMain />
                      <FSnackbar />
                      <DrawerCategories />
                    </NavigationContainer>
                  </MessageNotificationProvider>
                </NotificationBadgeListener>
              </IncomingCallProvider>
            </SafeAreaProvider>
          </GestureHandlerRootView>
        </PaperProvider>
      </LanguageProvider>
    </Provider>
  );

  // return isLoading ? (
  //   <View style={{backgroundColor: 'white', flex: 1}} />
  // ) : (
  //   <Provider store={store} stabilityCheck="always">
  //     <LanguageProvider>
  //       <PaperProvider>
  //         <GestureHandlerRootView>
  //           <StatusBar barStyle={'dark-content'} backgroundColor={'white'} />
  //           <SafeAreaProvider>
  //             <NavigationContainer ref={navigationRef}>
  //               <DrawerMain />
  //               <FSnackbar />
  //               <DrawerCategories />
  //             </NavigationContainer>
  //           </SafeAreaProvider>
  //         </GestureHandlerRootView>
  //       </PaperProvider>
  //     </LanguageProvider>
  //   </Provider>
  // );
}
export default App;
